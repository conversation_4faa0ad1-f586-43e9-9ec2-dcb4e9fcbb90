<?php

use App\Models\ContentSource;
use App\Models\FlowSession;
use App\Models\Language;
use App\Models\Question;
use App\Models\User;
use App\Models\UserProgress;
use Database\Seeders\ContentSourceSeeder;
use Database\Seeders\FlowSessionSeeder;
use Database\Seeders\LanguageSeeder;
use Database\Seeders\QuestionSeeder;
use Database\Seeders\UserProgressSeeder;

beforeEach(function () {
    // Seed the database with our test data
    $this->seed([
        LanguageSeeder::class,
        ContentSourceSeeder::class,
        QuestionSeeder::class,
        UserProgressSeeder::class,
        FlowSessionSeeder::class,
    ]);
});

test('japanese language is seeded correctly', function () {
    $japanese = Language::find('ja');

    expect($japanese)->not->toBeNull();
    expect($japanese->name)->toBe('Japanese');
    expect($japanese->native_name)->toBe('日本語');
    expect($japanese->writing_systems['scripts'])->toContain('hiragana', 'katakana', 'kanji');
});

test('minna no nihongo content sources are seeded', function () {
    $minnaSource = ContentSource::where('name', 'Minna no Nihongo Shokyu I')->first();

    expect($minnaSource)->not->toBeNull();
    expect($minnaSource->language_id)->toBe('ja');
    expect($minnaSource->type)->toBe('textbook');
    expect($minnaSource->configuration['publisher'])->toBe('3A Corporation');
    expect($minnaSource->configuration['totalLevels'])->toBe(25);
});

test('japanese questions are seeded with proper content', function () {
    $questions = Question::whereHas('contentSource', function ($query) {
        $query->where('name', 'Minna no Nihongo Shokyu I');
    })->get();

    expect($questions->count())->toBeGreaterThan(0);

    // Test a specific question
    $greetingQuestion = $questions->where('content.question_japanese', 'はじめまして')->first();
    expect($greetingQuestion)->not->toBeNull();
    expect($greetingQuestion->content['correct_answer'])->toBe(0);
    expect($greetingQuestion->metadata['chapter'])->toBe(1);
});

test('user progress is seeded with realistic data', function () {
    $userProgress = UserProgress::with(['user', 'language', 'contentSource'])->get();

    expect($userProgress->count())->toBeGreaterThan(0);

    $progress = $userProgress->first();
    expect($progress->language_id)->toBe('ja');
    expect($progress->progress_data)->toHaveKey('completion_percentage');
    expect($progress->flow_state)->toHaveKey('current_flow_type');
    expect($progress->flow_analytics)->toHaveKey('average_flow_score');
});

test('flow sessions are seeded with japanese learning data', function () {
    $flowSessions = FlowSession::where('language_id', 'ja')->get();

    expect($flowSessions->count())->toBeGreaterThan(0);

    $session = $flowSessions->first();
    expect($session->language_id)->toBe('ja');
    expect($session->flow_quality_score)->toBeGreaterThan(0);
    expect($session->session_metadata)->toHaveKey('session_type');
    expect($session->session_metadata)->toHaveKey('primary_focus');
    expect($session->session_metadata['learning_outcomes'])->toHaveKey('new_vocabulary_learned');
});

test('data relationships are properly established', function () {
    $user = User::with(['userProgress', 'flowSessions'])->first();

    expect($user->userProgress->count())->toBeGreaterThan(0);
    expect($user->flowSessions->count())->toBeGreaterThan(0);

    $progress = $user->userProgress->first();
    expect($progress->language->id)->toBe('ja');
    expect($progress->contentSource->name)->toContain('Minna no Nihongo');
});

test('questions have proper japanese learning structure', function () {
    $questions = Question::whereHas('contentSource', function ($query) {
        $query->where('language_id', 'ja');
    })->get();

    // Test different question types exist
    $questionTypes = $questions->pluck('question_type')->unique();
    expect($questionTypes)->toContain('multiple_choice');
    expect($questionTypes)->toContain('fill_blank');

    // Test Japanese-specific content
    $kanjiQuestion = $questions->where('question_type', 'kanji_reading')->first();
    if ($kanjiQuestion) {
        expect($kanjiQuestion->content)->toHaveKey('kanji');
        expect($kanjiQuestion->content)->toHaveKey('reading_type');
    }

    // Test flow sequence data
    $question = $questions->first();
    expect($question->flow_sequence)->toHaveKey('position');
    expect($question->flow_sequence)->toHaveKey('flow_type');
});
