<?php

use App\Models\ContentSource;
use App\Models\FlowSession;
use App\Models\Language;
use App\Models\User;
use App\Models\UserProgress;

test('language model can be created with proper attributes', function () {
    $language = Language::create([
        'id' => 'test',
        'name' => 'Test Language',
        'native_name' => 'Test Native',
        'writing_systems' => ['scripts' => ['latin']],
        'difficulty_factors' => ['grammarComplexity' => 5],
        'ai_config' => ['ttsVoices' => ['test-voice']],
        'flow_config' => ['sessionLength' => 15]
    ]);

    expect($language->id)->toBe('test');
    expect($language->name)->toBe('Test Language');
    expect($language->writing_systems)->toBeArray();
    expect($language->difficulty_factors)->toBeArray();
});

test('content source has proper relationship with language', function () {
    $language = Language::create([
        'id' => 'test',
        'name' => 'Test Language',
        'native_name' => 'Test Native',
        'writing_systems' => ['scripts' => ['latin']],
        'difficulty_factors' => ['grammarComplexity' => 5],
        'ai_config' => ['ttsVoices' => ['test-voice']],
        'flow_config' => ['sessionLength' => 15]
    ]);

    $contentSource = ContentSource::create([
        'language_id' => 'test',
        'name' => 'Test Textbook',
        'type' => 'textbook',
        'configuration' => ['levels' => ['A1', 'A2']],
        'flow_patterns' => ['sessionStructure' => 'warm_up|core_learning|practice']
    ]);

    expect($contentSource->language->id)->toBe('test');
    expect($contentSource->language->name)->toBe('Test Language');
    expect($language->contentSources->contains($contentSource))->toBeTrue();
});

test('user progress tracks relationships correctly', function () {
    $user = User::factory()->create();

    $language = Language::create([
        'id' => 'test',
        'name' => 'Test Language',
        'native_name' => 'Test Native',
        'writing_systems' => ['scripts' => ['latin']],
        'difficulty_factors' => ['grammarComplexity' => 5],
        'ai_config' => ['ttsVoices' => ['test-voice']],
        'flow_config' => ['sessionLength' => 15]
    ]);

    $contentSource = ContentSource::create([
        'language_id' => 'test',
        'name' => 'Test Textbook',
        'type' => 'textbook',
        'configuration' => ['levels' => ['A1', 'A2']],
        'flow_patterns' => ['sessionStructure' => 'warm_up|core_learning|practice']
    ]);

    $userProgress = UserProgress::create([
        'user_id' => $user->id,
        'language_id' => 'test',
        'source_id' => $contentSource->id,
        'current_level' => 'A1',
        'progress_data' => ['completion' => 25],
        'flow_state' => ['currentFlow' => 'vocabulary'],
        'flow_analytics' => ['averageScore' => 85],
        'last_activity' => now()
    ]);

    expect($userProgress->user->id)->toBe($user->id);
    expect($userProgress->language->id)->toBe('test');
    expect($userProgress->contentSource->id)->toBe($contentSource->id);
    expect($user->userProgress->contains($userProgress))->toBeTrue();
});

test('flow session tracks learning sessions with metrics', function () {
    $user = User::factory()->create();

    $language = Language::create([
        'id' => 'test',
        'name' => 'Test Language',
        'native_name' => 'Test Native',
        'writing_systems' => ['scripts' => ['latin']],
        'difficulty_factors' => ['grammarComplexity' => 5],
        'ai_config' => ['ttsVoices' => ['test-voice']],
        'flow_config' => ['sessionLength' => 15]
    ]);

    $flowSession = FlowSession::create([
        'user_id' => $user->id,
        'language_id' => 'test',
        'session_start' => now(),
        'session_end' => now()->addMinutes(20),
        'flow_quality_score' => 8.5,
        'questions_completed' => 15,
        'flow_interruptions' => 2,
        'session_metadata' => ['sessionType' => 'practice']
    ]);

    expect($flowSession->user->id)->toBe($user->id);
    expect($flowSession->language->id)->toBe('test');
    expect($flowSession->duration)->toBe(20.0);
    expect($flowSession->is_active)->toBeFalse();
});
