<?php

namespace Database\Seeders;

use App\Models\Language;
use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            [
                'id' => 'ja',
                'name' => 'Japanese',
                'native_name' => '日本語',
                'writing_systems' => [
                    'scripts' => ['hiragana', 'katakana', 'kanji'],
                    'direction' => 'ltr',
                    'hasSpaces' => false,
                    'complexCharacters' => true
                ],
                'difficulty_factors' => [
                    'grammarComplexity' => 8,
                    'writingSystemComplexity' => 10,
                    'pronunciationDifficulty' => 7
                ],
                'ai_config' => [
                    'ttsVoices' => ['ja-JP-Wavenet-A', 'ja-JP-Wavenet-B'],
                    'aiPromptTemplates' => [
                        'explanationPrompt' => 'Explain {concept} in Japanese grammar with smooth flow transitions...',
                        'conversationPrompt' => 'Create a natural Japanese conversation about {topic}...',
                        'grammarPrompt' => 'Explain the Japanese grammar pattern {pattern} with flow optimization...'
                    ]
                ],
                'flow_config' => [
                    'sessionLength' => 20,
                    'transitionPatterns' => ['hiragana_to_katakana', 'vocabulary_to_kanji', 'grammar_to_practice'],
                    'retentionStrategies' => ['spaced_repetition', 'contextual_review', 'stroke_order_practice']
                ]
            ],
            [
                'id' => 'es',
                'name' => 'Spanish',
                'native_name' => 'Español',
                'writing_systems' => [
                    'scripts' => ['latin'],
                    'direction' => 'ltr',
                    'hasSpaces' => true,
                    'complexCharacters' => false
                ],
                'difficulty_factors' => [
                    'grammarComplexity' => 6,
                    'writingSystemComplexity' => 2,
                    'pronunciationDifficulty' => 4
                ],
                'ai_config' => [
                    'ttsVoices' => ['es-ES-Wavenet-A', 'es-MX-Wavenet-A'],
                    'aiPromptTemplates' => [
                        'explanationPrompt' => 'Explain {concept} in Spanish grammar with natural flow...',
                        'conversationPrompt' => 'Create a Spanish conversation about {topic} with cultural context...',
                        'grammarPrompt' => 'Explain the Spanish grammar rule {rule} with smooth transitions...'
                    ]
                ],
                'flow_config' => [
                    'sessionLength' => 15,
                    'transitionPatterns' => ['vocabulary_to_grammar', 'reading_to_speaking', 'conjugation_to_usage'],
                    'retentionStrategies' => ['spaced_repetition', 'contextual_practice', 'cultural_immersion']
                ]
            ],
            [
                'id' => 'fr',
                'name' => 'French',
                'native_name' => 'Français',
                'writing_systems' => [
                    'scripts' => ['latin'],
                    'direction' => 'ltr',
                    'hasSpaces' => true,
                    'complexCharacters' => false
                ],
                'difficulty_factors' => [
                    'grammarComplexity' => 7,
                    'writingSystemComplexity' => 3,
                    'pronunciationDifficulty' => 8
                ],
                'ai_config' => [
                    'ttsVoices' => ['fr-FR-Wavenet-A', 'fr-CA-Wavenet-A'],
                    'aiPromptTemplates' => [
                        'explanationPrompt' => 'Explain {concept} in French grammar with flow optimization...',
                        'conversationPrompt' => 'Create a French conversation about {topic} with proper pronunciation flow...',
                        'grammarPrompt' => 'Explain the French grammar concept {concept} with smooth learning flow...'
                    ]
                ],
                'flow_config' => [
                    'sessionLength' => 18,
                    'transitionPatterns' => ['vocabulary_to_pronunciation', 'grammar_to_conversation', 'reading_to_writing'],
                    'retentionStrategies' => ['spaced_repetition', 'pronunciation_practice', 'cultural_context']
                ]
            ]
        ];

        foreach ($languages as $languageData) {
            Language::create($languageData);
        }
    }
}
