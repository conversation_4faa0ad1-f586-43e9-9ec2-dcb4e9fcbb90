<?php

namespace Database\Seeders;

use App\Models\ContentSource;
use App\Models\User;
use App\Models\UserProgress;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class UserProgressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some test users if they don't exist
        $users = User::factory(5)->create();

        $minnaSource = ContentSource::where('name', 'Minna no Nihongo Shokyu I')->first();

        if (!$minnaSource) {
            $this->command->error('Minna no Nihongo content source not found. Please run ContentSourceSeeder first.');
            return;
        }

        // Create progress records for different users at different levels
        foreach ($users as $index => $user) {
            $this->createUserProgress($user, $minnaSource, $index);
        }
    }

    private function createUserProgress($user, $contentSource, $userIndex)
    {
        // Different users at different progress levels
        $progressLevels = [
            [
                'current_level' => 'chapter_1',
                'completion_percentage' => 25,
                'chapters_completed' => 0,
                'flow_state' => 'vocabulary_learning'
            ],
            [
                'current_level' => 'chapter_2',
                'completion_percentage' => 60,
                'chapters_completed' => 1,
                'flow_state' => 'grammar_practice'
            ],
            [
                'current_level' => 'chapter_3',
                'completion_percentage' => 45,
                'chapters_completed' => 2,
                'flow_state' => 'conversation_practice'
            ],
            [
                'current_level' => 'chapter_4',
                'completion_percentage' => 80,
                'chapters_completed' => 3,
                'flow_state' => 'kanji_learning'
            ],
            [
                'current_level' => 'chapter_5',
                'completion_percentage' => 15,
                'chapters_completed' => 4,
                'flow_state' => 'verb_conjugation'
            ]
        ];

        $level = $progressLevels[$userIndex % count($progressLevels)];

        UserProgress::create([
            'id' => Str::uuid(),
            'user_id' => $user->id,
            'language_id' => 'ja',
            'source_id' => $contentSource->id,
            'current_level' => $level['current_level'],
            'progress_data' => [
                'completion_percentage' => $level['completion_percentage'],
                'chapters_completed' => $level['chapters_completed'],
                'total_chapters' => 25,
                'vocabulary_learned' => rand(50, 300),
                'kanji_learned' => rand(10, 80),
                'grammar_points_mastered' => rand(5, 25),
                'quiz_scores' => [
                    'vocabulary' => rand(70, 95),
                    'grammar' => rand(65, 90),
                    'kanji' => rand(60, 85),
                    'listening' => rand(70, 88)
                ],
                'study_streak' => rand(1, 30),
                'total_study_time_minutes' => rand(300, 2000),
                'last_quiz_score' => rand(70, 95),
                'weak_areas' => $this->getWeakAreas($level['current_level']),
                'strong_areas' => $this->getStrongAreas($level['current_level'])
            ],
            'flow_state' => [
                'current_flow_type' => $level['flow_state'],
                'flow_momentum' => rand(60, 95) / 100,
                'preferred_session_length' => rand(15, 45),
                'optimal_difficulty' => rand(3, 7),
                'last_flow_score' => rand(7, 10) / 10,
                'flow_interruptions_today' => rand(0, 3),
                'consecutive_flow_sessions' => rand(1, 8),
                'flow_preferences' => [
                    'morning_study' => rand(0, 1) == 1,
                    'audio_heavy' => rand(0, 1) == 1,
                    'visual_learning' => rand(0, 1) == 1,
                    'gamification' => rand(0, 1) == 1
                ]
            ],
            'flow_analytics' => [
                'average_flow_score' => rand(70, 90) / 10,
                'best_flow_score' => rand(85, 100) / 10,
                'flow_improvement_rate' => rand(5, 15) / 100,
                'optimal_study_times' => ['09:00', '14:00', '20:00'],
                'flow_patterns' => [
                    'vocabulary_to_grammar' => rand(80, 95) / 100,
                    'grammar_to_practice' => rand(75, 90) / 100,
                    'practice_to_conversation' => rand(70, 85) / 100
                ],
                'retention_rates' => [
                    'vocabulary' => rand(80, 95) / 100,
                    'grammar' => rand(75, 90) / 100,
                    'kanji' => rand(65, 85) / 100
                ],
                'session_completion_rate' => rand(85, 98) / 100,
                'average_session_duration' => rand(18, 35)
            ],
            'last_activity' => now()->subDays(rand(0, 7))->subHours(rand(0, 23))
        ]);
    }

    private function getWeakAreas($currentLevel)
    {
        $weakAreasByLevel = [
            'chapter_1' => ['particle_usage', 'pronunciation'],
            'chapter_2' => ['demonstratives', 'question_formation'],
            'chapter_3' => ['location_particles', 'spatial_concepts'],
            'chapter_4' => ['verb_conjugation', 'time_expressions'],
            'chapter_5' => ['movement_verbs', 'transportation_vocabulary']
        ];

        return $weakAreasByLevel[$currentLevel] ?? ['general_grammar'];
    }

    private function getStrongAreas($currentLevel)
    {
        $strongAreasByLevel = [
            'chapter_1' => ['greetings', 'basic_vocabulary'],
            'chapter_2' => ['noun_sentences', 'basic_particles'],
            'chapter_3' => ['demonstratives', 'place_vocabulary'],
            'chapter_4' => ['daily_activities', 'basic_verbs'],
            'chapter_5' => ['verb_forms', 'time_concepts']
        ];

        return $strongAreasByLevel[$currentLevel] ?? ['hiragana_reading'];
    }
}
