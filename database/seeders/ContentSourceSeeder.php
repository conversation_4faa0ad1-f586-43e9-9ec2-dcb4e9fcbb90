<?php

namespace Database\Seeders;

use App\Models\ContentSource;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ContentSourceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $minnaNoNihongo = ContentSource::create([
            'id' => Str::uuid(),
            'language_id' => 'ja',
            'name' => 'Minna no Nihongo Shokyu I',
            'type' => 'textbook',
            'configuration' => [
                'publisher' => '3A Corporation',
                'isbn' => '978-4883196036',
                'totalLevels' => 25,
                'levelSystem' => [
                    'type' => 'chapter',
                    'levels' => [
                        [
                            'levelId' => 'chapter_1',
                            'levelName' => 'Chapter 1 - はじめまして (Nice to meet you)',
                            'prerequisites' => [],
                            'estimatedHours' => 4,
                            'keyTopics' => ['greetings', 'self-introduction', 'basic_particles'],
                            'flowTransitions' => ['vocabulary_introduction', 'grammar_practice', 'conversation_practice']
                        ],
                        [
                            'levelId' => 'chapter_2',
                            'levelName' => 'Chapter 2 - これは辞書です (This is a dictionary)',
                            'prerequisites' => ['chapter_1'],
                            'estimatedHours' => 4,
                            'keyTopics' => ['demonstratives', 'noun_sentences', 'question_formation'],
                            'flowTransitions' => ['demonstrative_practice', 'noun_identification', 'question_answer_flow']
                        ],
                        [
                            'levelId' => 'chapter_3',
                            'levelName' => 'Chapter 3 - ここはデパートです (This is a department store)',
                            'prerequisites' => ['chapter_2'],
                            'estimatedHours' => 4,
                            'keyTopics' => ['location_words', 'place_descriptions', 'here_there_where'],
                            'flowTransitions' => ['location_vocabulary', 'spatial_relationships', 'place_conversations']
                        ],
                        [
                            'levelId' => 'chapter_4',
                            'levelName' => 'Chapter 4 - 起きます (Wake up)',
                            'prerequisites' => ['chapter_3'],
                            'estimatedHours' => 5,
                            'keyTopics' => ['verb_conjugation', 'daily_activities', 'time_expressions'],
                            'flowTransitions' => ['verb_introduction', 'conjugation_practice', 'daily_routine_flow']
                        ],
                        [
                            'levelId' => 'chapter_5',
                            'levelName' => 'Chapter 5 - 行きます (Go)',
                            'prerequisites' => ['chapter_4'],
                            'estimatedHours' => 5,
                            'keyTopics' => ['movement_verbs', 'transportation', 'destination_particles'],
                            'flowTransitions' => ['movement_vocabulary', 'particle_practice', 'travel_conversations']
                        ]
                    ]
                ],
                'contentTypes' => ['vocabulary', 'grammar', 'kanji', 'audio', 'conversation'],
                'assessmentTypes' => ['multiple_choice', 'fill_blank', 'audio_recognition', 'kanji_writing', 'conversation']
            ],
            'flow_patterns' => [
                'sessionStructure' => 'vocabulary_warmup|grammar_introduction|practice_exercises|conversation_application|review',
                'transitionTypes' => ['hiragana_to_kanji', 'vocabulary_to_grammar', 'grammar_to_conversation'],
                'retentionStrategies' => ['spaced_repetition', 'kanji_stroke_practice', 'audio_repetition', 'contextual_usage']
            ],
            'is_active' => true
        ]);

        // Create Minna no Nihongo Shokyu II
        ContentSource::create([
            'id' => Str::uuid(),
            'language_id' => 'ja',
            'name' => 'Minna no Nihongo Shokyu II',
            'type' => 'textbook',
            'configuration' => [
                'publisher' => '3A Corporation',
                'isbn' => '978-4883196043',
                'totalLevels' => 25,
                'levelSystem' => [
                    'type' => 'chapter',
                    'levels' => [
                        [
                            'levelId' => 'chapter_26',
                            'levelName' => 'Chapter 26 - 見えます (Can see)',
                            'prerequisites' => ['minna_1_complete'],
                            'estimatedHours' => 6,
                            'keyTopics' => ['potential_form', 'ability_expressions', 'sensory_verbs'],
                            'flowTransitions' => ['potential_introduction', 'ability_practice', 'sensory_application']
                        ],
                        [
                            'levelId' => 'chapter_27',
                            'levelName' => 'Chapter 27 - 読んだことがあります (Have read before)',
                            'prerequisites' => ['chapter_26'],
                            'estimatedHours' => 6,
                            'keyTopics' => ['experience_expressions', 'past_experience', 'koto_ga_aru'],
                            'flowTransitions' => ['experience_vocabulary', 'grammar_patterns', 'personal_experiences']
                        ]
                    ]
                ],
                'contentTypes' => ['vocabulary', 'grammar', 'kanji', 'audio', 'conversation', 'reading'],
                'assessmentTypes' => ['multiple_choice', 'fill_blank', 'audio_recognition', 'kanji_writing', 'conversation', 'reading_comprehension']
            ],
            'flow_patterns' => [
                'sessionStructure' => 'review_previous|new_grammar_introduction|pattern_practice|reading_application|conversation_practice',
                'transitionTypes' => ['grammar_to_reading', 'vocabulary_to_kanji', 'practice_to_application'],
                'retentionStrategies' => ['grammar_pattern_drilling', 'reading_comprehension', 'advanced_conversation', 'kanji_compound_practice']
            ],
            'is_active' => true
        ]);
    }
}
