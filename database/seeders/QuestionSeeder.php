<?php

namespace Database\Seeders;

use App\Models\ContentSource;
use App\Models\Question;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class QuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $minnaSource = ContentSource::where('name', 'Minna no Nihongo Shokyu I')->first();

        if (!$minnaSource) {
            $this->command->error('Minna no Nihongo content source not found. Please run ContentSourceSeeder first.');
            return;
        }

        // Chapter 1 Questions - Greetings and Self-introduction
        $this->createChapter1Questions($minnaSource->id);

        // Chapter 2 Questions - Demonstratives and Noun Sentences
        $this->createChapter2Questions($minnaSource->id);

        // Chapter 3 Questions - Location and Place Descriptions
        $this->createChapter3Questions($minnaSource->id);

        // Chapter 4 Questions - Verbs and Daily Activities
        $this->createChapter4Questions($minnaSource->id);
    }

    private function createChapter1Questions($sourceId)
    {
        // Vocabulary Question - Greetings
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'multiple_choice',
            'difficulty_level' => 1,
            'content' => [
                'question_text' => 'What does "はじめまして" mean?',
                'question_japanese' => 'はじめまして',
                'options' => [
                    'Nice to meet you',
                    'Good morning',
                    'Thank you',
                    'Excuse me'
                ],
                'correct_answer' => 0,
                'explanation' => 'はじめまして (hajimemashite) is used when meeting someone for the first time.'
            ],
            'metadata' => [
                'chapter' => 1,
                'topic' => 'greetings',
                'skill_type' => 'vocabulary',
                'tags' => ['greeting', 'first_meeting', 'polite_expression']
            ],
            'flow_sequence' => [
                'position' => 1,
                'prerequisite_concepts' => [],
                'next_concepts' => ['self_introduction'],
                'flow_type' => 'vocabulary_introduction'
            ]
        ]);

        // Grammar Question - Particle は
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'fill_blank',
            'difficulty_level' => 2,
            'content' => [
                'question_text' => 'Fill in the blank with the correct particle:',
                'sentence_template' => 'わたし___田中です。',
                'sentence_romaji' => 'Watashi ___ Tanaka desu.',
                'correct_answer' => 'は',
                'options' => ['は', 'が', 'を', 'に'],
                'explanation' => 'The particle は (wa) marks the topic of the sentence. "わたしは田中です" means "I am Tanaka."'
            ],
            'metadata' => [
                'chapter' => 1,
                'topic' => 'particles',
                'skill_type' => 'grammar',
                'tags' => ['particle_wa', 'topic_marker', 'self_introduction']
            ],
            'flow_sequence' => [
                'position' => 2,
                'prerequisite_concepts' => ['greetings'],
                'next_concepts' => ['desu_sentences'],
                'flow_type' => 'grammar_introduction'
            ]
        ]);

        // Audio Recognition Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'audio_recognition',
            'difficulty_level' => 2,
            'content' => [
                'question_text' => 'Listen and choose the correct hiragana:',
                'audio_text' => 'わたし',
                'audio_romaji' => 'watashi',
                'options' => ['わたし', 'あたし', 'わだし', 'あだし'],
                'correct_answer' => 0,
                'explanation' => 'わたし (watashi) means "I" and is the standard polite form.'
            ],
            'metadata' => [
                'chapter' => 1,
                'topic' => 'pronunciation',
                'skill_type' => 'listening',
                'tags' => ['hiragana', 'pronunciation', 'personal_pronouns']
            ],
            'flow_sequence' => [
                'position' => 3,
                'prerequisite_concepts' => ['hiragana_basics'],
                'next_concepts' => ['pronunciation_practice'],
                'flow_type' => 'audio_practice'
            ]
        ]);
    }

    private function createChapter2Questions($sourceId)
    {
        // Demonstrative Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'multiple_choice',
            'difficulty_level' => 3,
            'content' => [
                'question_text' => 'Which demonstrative is used for something close to the speaker?',
                'context' => 'Demonstratives in Japanese: これ, それ, あれ',
                'options' => ['これ (kore)', 'それ (sore)', 'あれ (are)', 'どれ (dore)'],
                'correct_answer' => 0,
                'explanation' => 'これ (kore) is used for objects close to the speaker.'
            ],
            'metadata' => [
                'chapter' => 2,
                'topic' => 'demonstratives',
                'skill_type' => 'grammar',
                'tags' => ['demonstratives', 'spatial_relationships', 'kore_sore_are']
            ],
            'flow_sequence' => [
                'position' => 1,
                'prerequisite_concepts' => ['basic_particles'],
                'next_concepts' => ['noun_sentences'],
                'flow_type' => 'grammar_concept'
            ]
        ]);

        // Sentence Construction Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'sentence_construction',
            'difficulty_level' => 4,
            'content' => [
                'question_text' => 'Arrange the words to form a correct sentence meaning "This is a dictionary":',
                'words' => ['これ', 'は', '辞書', 'です'],
                'correct_order' => [0, 1, 2, 3],
                'correct_sentence' => 'これは辞書です。',
                'romaji' => 'Kore wa jisho desu.',
                'explanation' => 'The basic sentence pattern is: これ (this) + は (topic particle) + 辞書 (dictionary) + です (is).'
            ],
            'metadata' => [
                'chapter' => 2,
                'topic' => 'sentence_structure',
                'skill_type' => 'grammar',
                'tags' => ['sentence_construction', 'noun_sentences', 'desu_sentences']
            ],
            'flow_sequence' => [
                'position' => 2,
                'prerequisite_concepts' => ['demonstratives', 'particle_wa'],
                'next_concepts' => ['question_formation'],
                'flow_type' => 'sentence_building'
            ]
        ]);
    }

    private function createChapter3Questions($sourceId)
    {
        // Location Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'multiple_choice',
            'difficulty_level' => 3,
            'content' => [
                'question_text' => 'What does "ここ" mean?',
                'context' => 'Location words: ここ, そこ, あそこ',
                'options' => ['here', 'there (near you)', 'over there', 'where'],
                'correct_answer' => 0,
                'explanation' => 'ここ (koko) means "here" - a place close to the speaker.'
            ],
            'metadata' => [
                'chapter' => 3,
                'topic' => 'location_words',
                'skill_type' => 'vocabulary',
                'tags' => ['location', 'spatial_words', 'koko_soko_asoko']
            ],
            'flow_sequence' => [
                'position' => 1,
                'prerequisite_concepts' => ['demonstratives'],
                'next_concepts' => ['place_descriptions'],
                'flow_type' => 'vocabulary_expansion'
            ]
        ]);

        // Place Description Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'fill_blank',
            'difficulty_level' => 4,
            'content' => [
                'question_text' => 'Complete the sentence: "Here is a department store"',
                'sentence_template' => 'ここ___デパートです。',
                'sentence_romaji' => 'Koko ___ depaato desu.',
                'correct_answer' => 'は',
                'options' => ['は', 'が', 'を', 'に'],
                'explanation' => 'ここは (koko wa) - "here" with topic particle は to indicate what we are talking about.'
            ],
            'metadata' => [
                'chapter' => 3,
                'topic' => 'place_descriptions',
                'skill_type' => 'grammar',
                'tags' => ['location_sentences', 'particle_wa', 'place_vocabulary']
            ],
            'flow_sequence' => [
                'position' => 2,
                'prerequisite_concepts' => ['location_words', 'particle_wa'],
                'next_concepts' => ['place_conversations'],
                'flow_type' => 'grammar_application'
            ]
        ]);
    }

    private function createChapter4Questions($sourceId)
    {
        // Verb Introduction Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'multiple_choice',
            'difficulty_level' => 5,
            'content' => [
                'question_text' => 'What is the polite form of the verb "to wake up"?',
                'context' => 'Daily activity verbs',
                'options' => ['起きます', '起きる', '起きた', '起きて'],
                'correct_answer' => 0,
                'explanation' => '起きます (okimasu) is the polite present form of 起きる (okiru) meaning "to wake up".'
            ],
            'metadata' => [
                'chapter' => 4,
                'topic' => 'verb_conjugation',
                'skill_type' => 'grammar',
                'tags' => ['verbs', 'polite_form', 'daily_activities', 'masu_form']
            ],
            'flow_sequence' => [
                'position' => 1,
                'prerequisite_concepts' => ['noun_sentences'],
                'next_concepts' => ['verb_conjugation'],
                'flow_type' => 'grammar_introduction'
            ]
        ]);

        // Time Expression Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'fill_blank',
            'difficulty_level' => 6,
            'content' => [
                'question_text' => 'Complete the sentence: "I wake up at 7 o\'clock"',
                'sentence_template' => '7時___起きます。',
                'sentence_romaji' => 'Shichi-ji ___ okimasu.',
                'correct_answer' => 'に',
                'options' => ['に', 'で', 'を', 'と'],
                'explanation' => 'The particle に is used with specific times. 7時に (shichi-ji ni) means "at 7 o\'clock".'
            ],
            'metadata' => [
                'chapter' => 4,
                'topic' => 'time_expressions',
                'skill_type' => 'grammar',
                'tags' => ['time_particles', 'particle_ni', 'daily_schedule', 'time_expressions']
            ],
            'flow_sequence' => [
                'position' => 2,
                'prerequisite_concepts' => ['verb_masu_form', 'numbers'],
                'next_concepts' => ['daily_routine'],
                'flow_type' => 'grammar_application'
            ]
        ]);

        // Kanji Recognition Question
        Question::create([
            'id' => Str::uuid(),
            'source_id' => $sourceId,
            'question_type' => 'kanji_reading',
            'difficulty_level' => 7,
            'content' => [
                'question_text' => 'What is the reading of this kanji?',
                'kanji' => '起',
                'context' => 'From the verb 起きます (to wake up)',
                'options' => ['お', 'き', 'こ', 'く'],
                'correct_answer' => 1,
                'reading_type' => 'kun_reading',
                'explanation' => '起 is read as "き" (ki) in 起きます. This is the kun-reading of the kanji.'
            ],
            'metadata' => [
                'chapter' => 4,
                'topic' => 'kanji_reading',
                'skill_type' => 'kanji',
                'tags' => ['kanji', 'kun_reading', 'daily_activities', 'verb_kanji']
            ],
            'flow_sequence' => [
                'position' => 3,
                'prerequisite_concepts' => ['hiragana_mastery', 'verb_vocabulary'],
                'next_concepts' => ['kanji_writing'],
                'flow_type' => 'kanji_introduction'
            ]
        ]);
    }
}
