<?php

namespace Database\Seeders;

use App\Models\FlowSession;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class FlowSessionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->error('No users found. Please run UserProgressSeeder first to create users.');
            return;
        }

        // Create flow sessions for each user over the past 30 days
        foreach ($users as $user) {
            $this->createFlowSessionsForUser($user);
        }
    }

    private function createFlowSessionsForUser($user)
    {
        // Create 10-20 sessions per user over the past 30 days
        $sessionCount = rand(10, 20);

        for ($i = 0; $i < $sessionCount; $i++) {
            $this->createFlowSession($user, $i);
        }
    }

    private function createFlowSession($user, $sessionIndex)
    {
        // Random session start time in the past 30 days
        $sessionStart = now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

        // Session duration between 10-45 minutes
        $durationMinutes = rand(10, 45);
        $sessionEnd = $sessionStart->copy()->addMinutes($durationMinutes);

        // Flow quality varies based on session characteristics
        $questionsCompleted = rand(8, 25);
        $flowInterruptions = rand(0, 4);

        // Better flow scores for longer sessions with fewer interruptions
        $baseFlowScore = 6.0;
        if ($durationMinutes > 25)
            $baseFlowScore += 1.0;
        if ($flowInterruptions == 0)
            $baseFlowScore += 1.5;
        if ($questionsCompleted > 15)
            $baseFlowScore += 0.5;

        $flowQualityScore = min(10.0, $baseFlowScore + (rand(-10, 10) / 10));

        FlowSession::create([
            'id' => Str::uuid(),
            'user_id' => $user->id,
            'language_id' => 'ja',
            'session_start' => $sessionStart,
            'session_end' => $sessionEnd,
            'flow_quality_score' => round($flowQualityScore, 2),
            'questions_completed' => $questionsCompleted,
            'flow_interruptions' => $flowInterruptions,
            'session_metadata' => [
                'session_type' => $this->getRandomSessionType(),
                'primary_focus' => $this->getRandomFocus(),
                'difficulty_level' => rand(1, 7),
                'study_materials' => $this->getRandomStudyMaterials(),
                'performance_metrics' => [
                    'accuracy_rate' => rand(70, 95) / 100,
                    'response_time_avg' => rand(3, 8),
                    'hints_used' => rand(0, 5),
                    'mistakes_made' => rand(0, 8)
                ],
                'flow_metrics' => [
                    'flow_state_duration' => rand(60, 90) / 100 * $durationMinutes,
                    'concentration_level' => rand(70, 95) / 100,
                    'engagement_score' => rand(75, 98) / 100,
                    'challenge_balance' => rand(65, 90) / 100
                ],
                'learning_outcomes' => [
                    'new_vocabulary_learned' => rand(3, 12),
                    'grammar_points_practiced' => rand(1, 5),
                    'kanji_reviewed' => rand(2, 8),
                    'pronunciation_attempts' => rand(5, 15)
                ],
                'session_notes' => $this->getRandomSessionNotes($flowQualityScore),
                'device_info' => [
                    'platform' => rand(0, 1) ? 'mobile' : 'desktop',
                    'study_environment' => $this->getRandomEnvironment()
                ]
            ]
        ]);
    }

    private function getRandomSessionType()
    {
        $types = [
            'vocabulary_practice',
            'grammar_focus',
            'kanji_study',
            'conversation_practice',
            'mixed_review',
            'pronunciation_training',
            'reading_comprehension'
        ];

        return $types[array_rand($types)];
    }

    private function getRandomFocus()
    {
        $focuses = [
            'chapter_1_greetings',
            'chapter_2_demonstratives',
            'chapter_3_locations',
            'chapter_4_verbs',
            'particle_practice',
            'kanji_recognition',
            'daily_vocabulary',
            'sentence_construction'
        ];

        return $focuses[array_rand($focuses)];
    }

    private function getRandomStudyMaterials()
    {
        $materials = [];
        $allMaterials = [
            'minna_no_nihongo_textbook',
            'audio_exercises',
            'flashcards',
            'writing_practice',
            'conversation_drills',
            'kanji_stroke_order',
            'grammar_explanations'
        ];

        // Select 2-4 random materials
        $count = rand(2, 4);
        $selectedMaterials = array_rand($allMaterials, $count);

        if (is_array($selectedMaterials)) {
            foreach ($selectedMaterials as $index) {
                $materials[] = $allMaterials[$index];
            }
        } else {
            $materials[] = $allMaterials[$selectedMaterials];
        }

        return $materials;
    }

    private function getRandomSessionNotes($flowScore)
    {
        if ($flowScore >= 8.0) {
            $notes = [
                'Excellent flow state maintained throughout session',
                'Strong focus and engagement with material',
                'Smooth transitions between different exercise types',
                'High retention rate observed'
            ];
        } elseif ($flowScore >= 6.0) {
            $notes = [
                'Good learning session with minor interruptions',
                'Steady progress through material',
                'Some difficulty with new concepts but overall positive',
                'Maintained concentration for most of session'
            ];
        } else {
            $notes = [
                'Session had several interruptions affecting flow',
                'Struggled with some concepts, may need review',
                'Shorter session might be more effective',
                'Consider adjusting difficulty level'
            ];
        }

        return $notes[array_rand($notes)];
    }

    private function getRandomEnvironment()
    {
        $environments = [
            'quiet_room',
            'library',
            'coffee_shop',
            'commute',
            'home_office',
            'bedroom',
            'study_group'
        ];

        return $environments[array_rand($environments)];
    }
}
