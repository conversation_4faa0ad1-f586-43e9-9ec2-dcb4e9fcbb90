<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed in the correct order due to foreign key dependencies
        $this->call([
            LanguageSeeder::class,
            ContentSourceSeeder::class,
            QuestionSeeder::class,
            UserProgressSeeder::class,  // This creates users and their progress
            FlowSessionSeeder::class,
            AdminSeeder::class,  // Create admin user
        ]);

        // Create a test user for development
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }
}
