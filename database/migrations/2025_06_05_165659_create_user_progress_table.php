<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_progress', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('language_id', 10);
            $table->uuid('source_id');
            $table->string('current_level', 100); // Chapter 1, A1, N5, etc.
            $table->json('progress_data'); // Completion percentages, scores, etc.
            $table->json('flow_state'); // Current flow state and preferences
            $table->json('flow_analytics'); // Flow performance metrics
            $table->timestamp('last_activity');
            $table->timestamps();

            $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            $table->foreign('source_id')->references('id')->on('content_sources')->onDelete('cascade');
            $table->unique(['user_id', 'language_id', 'source_id']);
            $table->index(['user_id', 'last_activity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_progress');
    }
};
