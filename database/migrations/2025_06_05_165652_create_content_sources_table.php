<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_sources', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('language_id', 10);
            $table->string('name', 200); // Source name like 'Minna no Nihongo'
            $table->enum('type', ['textbook', 'course', 'custom', 'community']);
            $table->json('configuration'); // Level system, content types, assessment types
            $table->json('flow_patterns'); // Session structure, transition types
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            $table->index(['language_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_sources');
    }
};
