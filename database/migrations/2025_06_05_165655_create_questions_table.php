<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('source_id');
            $table->string('question_type', 50); // multiple_choice, fill_blank, audio, etc.
            $table->integer('difficulty_level'); // 1-10 scale
            $table->json('content'); // Question text, options, correct answer, etc.
            $table->json('metadata'); // Tags, topics, language-specific data
            $table->json('flow_sequence'); // Flow optimization data
            $table->timestamps();

            $table->foreign('source_id')->references('id')->on('content_sources')->onDelete('cascade');
            $table->index(['source_id', 'question_type']);
            $table->index(['difficulty_level']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
