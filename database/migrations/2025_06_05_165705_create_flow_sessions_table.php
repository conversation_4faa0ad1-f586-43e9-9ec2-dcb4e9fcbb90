<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flow_sessions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('language_id', 10);
            $table->timestamp('session_start');
            $table->timestamp('session_end')->nullable();
            $table->decimal('flow_quality_score', 3, 2)->nullable(); // 0.00 to 10.00
            $table->integer('questions_completed')->default(0);
            $table->integer('flow_interruptions')->default(0);
            $table->json('session_metadata'); // Session details, performance data
            $table->timestamps();

            $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            $table->index(['user_id', 'session_start']);
            $table->index(['language_id', 'session_start']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flow_sessions');
    }
};
