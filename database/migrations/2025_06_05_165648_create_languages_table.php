<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('languages', function (Blueprint $table) {
            $table->string('id', 10)->primary(); // Language code like 'ja', 'es', 'ar'
            $table->string('name', 100); // English name like 'Japanese'
            $table->string('native_name', 100); // Native name like '日本語'
            $table->json('writing_systems'); // Scripts, direction, spacing info
            $table->json('difficulty_factors'); // Grammar, writing, pronunciation complexity
            $table->json('ai_config'); // AI prompt templates and TTS voices
            $table->json('flow_config'); // Flow optimization settings
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('languages');
    }
};
