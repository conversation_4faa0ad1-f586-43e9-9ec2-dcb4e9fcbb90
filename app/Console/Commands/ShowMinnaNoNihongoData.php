<?php

namespace App\Console\Commands;

use App\Models\ContentSource;
use App\Models\FlowSession;
use App\Models\Language;
use App\Models\Question;
use App\Models\User;
use App\Models\UserProgress;
use Illuminate\Console\Command;

class ShowMinnaNoNihongoData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'minna:show-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display seeded Minna no Nihongo learning data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🇯🇵 Minna no Nihongo Learning Data Overview');
        $this->newLine();

        // Show Languages
        $this->showLanguages();
        $this->newLine();

        // Show Content Sources
        $this->showContentSources();
        $this->newLine();

        // Show Questions
        $this->showQuestions();
        $this->newLine();

        // Show User Progress
        $this->showUserProgress();
        $this->newLine();

        // Show Flow Sessions
        $this->showFlowSessions();
    }

    private function showLanguages()
    {
        $this->info('📚 Languages:');
        $languages = Language::all();

        foreach ($languages as $language) {
            $this->line("  • {$language->id}: {$language->name} ({$language->native_name})");
            $this->line("    Writing Systems: " . implode(', ', $language->writing_systems['scripts']));
            $this->line("    Difficulty: Grammar({$language->difficulty_factors['grammarComplexity']}/10), Writing({$language->difficulty_factors['writingSystemComplexity']}/10)");
        }
    }

    private function showContentSources()
    {
        $this->info('📖 Content Sources:');
        $sources = ContentSource::with('language')->get();

        foreach ($sources as $source) {
            $this->line("  • {$source->name} ({$source->language->name})");
            $this->line("    Type: {$source->type}");
            $this->line("    Publisher: {$source->configuration['publisher']}");
            $this->line("    Total Levels: {$source->configuration['totalLevels']}");
        }
    }

    private function showQuestions()
    {
        $this->info('❓ Questions:');
        $questionStats = Question::selectRaw('question_type, COUNT(*) as count')
            ->groupBy('question_type')
            ->get();

        $totalQuestions = Question::count();
        $this->line("  Total Questions: {$totalQuestions}");

        foreach ($questionStats as $stat) {
            $this->line("  • {$stat->question_type}: {$stat->count} questions");
        }

        // Show a sample question
        $sampleQuestion = Question::where('question_type', 'multiple_choice')->first();
        if ($sampleQuestion) {
            $this->newLine();
            $this->line("  📝 Sample Question:");
            $this->line("    Type: {$sampleQuestion->question_type}");
            $this->line("    Chapter: {$sampleQuestion->metadata['chapter']}");
            $this->line("    Question: {$sampleQuestion->content['question_text']}");
            if (isset($sampleQuestion->content['question_japanese'])) {
                $this->line("    Japanese: {$sampleQuestion->content['question_japanese']}");
            }
        }
    }

    private function showUserProgress()
    {
        $this->info('👥 User Progress:');
        $userCount = User::count();
        $progressCount = UserProgress::count();

        $this->line("  Total Users: {$userCount}");
        $this->line("  Total Progress Records: {$progressCount}");

        // Calculate average completion using PHP instead of SQL JSON functions
        $allProgress = UserProgress::all();
        $totalCompletion = 0;
        $count = 0;

        foreach ($allProgress as $progress) {
            if (isset($progress->progress_data['completion_percentage'])) {
                $totalCompletion += $progress->progress_data['completion_percentage'];
                $count++;
            }
        }

        if ($count > 0) {
            $avgCompletion = $totalCompletion / $count;
            $this->line("  Average Completion: " . round($avgCompletion, 1) . "%");
        }

        // Show progress by level
        $progressByLevel = UserProgress::selectRaw('current_level, COUNT(*) as count')
            ->groupBy('current_level')
            ->get();

        $this->line("  Progress by Chapter:");
        foreach ($progressByLevel as $progress) {
            $this->line("    • {$progress->current_level}: {$progress->count} users");
        }
    }

    private function showFlowSessions()
    {
        $this->info('🌊 Flow Sessions:');
        $sessionCount = FlowSession::count();
        $avgFlowScore = FlowSession::avg('flow_quality_score');

        $this->line("  Total Sessions: {$sessionCount}");
        $this->line("  Average Flow Score: " . round($avgFlowScore, 2) . "/10");

        // Calculate average duration using PHP
        $sessions = FlowSession::whereNotNull('session_end')->get();
        $totalDuration = 0;
        $completedSessions = 0;

        foreach ($sessions as $session) {
            if ($session->session_end) {
                $totalDuration += $session->duration;
                $completedSessions++;
            }
        }

        if ($completedSessions > 0) {
            $avgDuration = $totalDuration / $completedSessions;
            $this->line("  Average Duration: " . round($avgDuration, 1) . " minutes");
        }

        // Show session types using PHP
        $sessionTypes = [];
        $allSessions = FlowSession::all();

        foreach ($allSessions as $session) {
            $sessionType = $session->session_metadata['session_type'] ?? 'unknown';
            $sessionTypes[$sessionType] = ($sessionTypes[$sessionType] ?? 0) + 1;
        }

        $this->line("  Session Types:");
        foreach ($sessionTypes as $type => $count) {
            $this->line("    • {$type}: {$count} sessions");
        }
    }
}
