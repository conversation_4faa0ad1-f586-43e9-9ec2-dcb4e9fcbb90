<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Question extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'source_id',
        'question_type',
        'difficulty_level',
        'content',
        'metadata',
        'flow_sequence',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'content' => 'array',
        'metadata' => 'array',
        'flow_sequence' => 'array',
        'difficulty_level' => 'integer',
    ];

    /**
     * Get the content source that owns this question.
     */
    public function contentSource(): BelongsTo
    {
        return $this->belongsTo(ContentSource::class, 'source_id');
    }

    /**
     * Get the language through the content source.
     */
    public function language()
    {
        return $this->contentSource->language();
    }

    /**
     * Scope a query to filter by question type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('question_type', $type);
    }

    /**
     * Scope a query to filter by difficulty level.
     */
    public function scopeOfDifficulty($query, $level)
    {
        return $query->where('difficulty_level', $level);
    }

    /**
     * Scope a query to filter by difficulty range.
     */
    public function scopeOfDifficultyRange($query, $minLevel, $maxLevel)
    {
        return $query->whereBetween('difficulty_level', [$minLevel, $maxLevel]);
    }
}
