<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FlowSession extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'language_id',
        'session_start',
        'session_end',
        'flow_quality_score',
        'questions_completed',
        'flow_interruptions',
        'session_metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'session_start' => 'datetime',
        'session_end' => 'datetime',
        'flow_quality_score' => 'decimal:2',
        'questions_completed' => 'integer',
        'flow_interruptions' => 'integer',
        'session_metadata' => 'array',
    ];

    /**
     * Get the user that owns this flow session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the language for this flow session.
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_id');
    }

    /**
     * Scope a query to filter by user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to filter by language.
     */
    public function scopeForLanguage($query, $languageId)
    {
        return $query->where('language_id', $languageId);
    }

    /**
     * Scope a query to get completed sessions.
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('session_end');
    }

    /**
     * Scope a query to get active sessions.
     */
    public function scopeActive($query)
    {
        return $query->whereNull('session_end');
    }

    /**
     * Scope a query to get sessions within a date range.
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('session_start', [$startDate, $endDate]);
    }

    /**
     * Calculate session duration in minutes.
     */
    public function getDurationAttribute()
    {
        if (!$this->session_end) {
            return null;
        }

        return $this->session_start->diffInMinutes($this->session_end);
    }

    /**
     * Check if the session is currently active.
     */
    public function getIsActiveAttribute()
    {
        return $this->session_end === null;
    }
}
