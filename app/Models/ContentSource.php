<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ContentSource extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'language_id',
        'name',
        'type',
        'configuration',
        'flow_patterns',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'configuration' => 'array',
        'flow_patterns' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the language that owns this content source.
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_id');
    }

    /**
     * Get the questions for this content source.
     */
    public function questions(): Has<PERSON>any
    {
        return $this->hasMany(Question::class, 'source_id');
    }

    /**
     * Get the user progress records for this content source.
     */
    public function userProgress(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(UserProgress::class, 'source_id');
    }

    /**
     * Scope a query to only include active content sources.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by content source type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}
