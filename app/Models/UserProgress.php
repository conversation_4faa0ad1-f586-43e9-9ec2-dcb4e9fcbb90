<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProgress extends Model
{
    use HasUuids;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_progress';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'language_id',
        'source_id',
        'current_level',
        'progress_data',
        'flow_state',
        'flow_analytics',
        'last_activity',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'progress_data' => 'array',
        'flow_state' => 'array',
        'flow_analytics' => 'array',
        'last_activity' => 'datetime',
    ];

    /**
     * Get the user that owns this progress record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the language for this progress record.
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_id');
    }

    /**
     * Get the content source for this progress record.
     */
    public function contentSource(): BelongsTo
    {
        return $this->belongsTo(ContentSource::class, 'source_id');
    }

    /**
     * Scope a query to filter by user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to filter by language.
     */
    public function scopeForLanguage($query, $languageId)
    {
        return $query->where('language_id', $languageId);
    }

    /**
     * Scope a query to get recent activity.
     */
    public function scopeRecentActivity($query, $days = 30)
    {
        return $query->where('last_activity', '>=', now()->subDays($days));
    }
}
