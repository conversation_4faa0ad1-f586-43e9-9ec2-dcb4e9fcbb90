<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Language extends Model
{
    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * The "type" of the primary key ID.
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'name',
        'native_name',
        'writing_systems',
        'difficulty_factors',
        'ai_config',
        'flow_config',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'writing_systems' => 'array',
        'difficulty_factors' => 'array',
        'ai_config' => 'array',
        'flow_config' => 'array',
    ];

    /**
     * Get the content sources for this language.
     */
    public function contentSources(): HasMany
    {
        return $this->hasMany(ContentSource::class, 'language_id');
    }

    /**
     * Get the user progress records for this language.
     */
    public function userProgress(): HasMany
    {
        return $this->hasMany(UserProgress::class, 'language_id');
    }

    /**
     * Get the flow sessions for this language.
     */
    public function flowSessions(): HasMany
    {
        return $this->hasMany(FlowSession::class, 'language_id');
    }
}
