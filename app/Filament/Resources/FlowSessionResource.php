<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FlowSessionResource\Pages;
use App\Filament\Resources\FlowSessionResource\RelationManagers;
use App\Models\FlowSession;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FlowSessionResource extends Resource
{
    protected static ?string $model = FlowSession::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationGroup = 'Learning Analytics';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Select::make('language_id')
                    ->relationship('language', 'name')
                    ->required(),
                Forms\Components\DateTimePicker::make('session_start')
                    ->required()
                    ->default(now()),
                Forms\Components\DateTimePicker::make('session_end')
                    ->helperText('Leave empty for active sessions'),
                Forms\Components\TextInput::make('flow_quality_score')
                    ->numeric()
                    ->step(0.01)
                    ->minValue(0)
                    ->maxValue(10)
                    ->helperText('Flow quality score from 0.00 to 10.00'),
                Forms\Components\TextInput::make('questions_completed')
                    ->required()
                    ->numeric()
                    ->default(0)
                    ->minValue(0),
                Forms\Components\TextInput::make('flow_interruptions')
                    ->required()
                    ->numeric()
                    ->default(0)
                    ->minValue(0),
                Forms\Components\KeyValue::make('session_metadata')
                    ->label('Session Metadata')
                    ->required()
                    ->helperText('JSON object with session details and performance data'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('language.name')
                    ->label('Language')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('session_start')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('session_end')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Active'),
                Tables\Columns\TextColumn::make('duration')
                    ->label('Duration (min)')
                    ->getStateUsing(
                        fn(FlowSession $record): ?string =>
                        $record->session_end
                        ? $record->session_start->diffInMinutes($record->session_end) . ' min'
                        : 'Active'
                    )
                    ->badge()
                    ->color(
                        fn(FlowSession $record): string =>
                        $record->session_end ? 'success' : 'warning'
                    ),
                Tables\Columns\TextColumn::make('flow_quality_score')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(fn(?string $state): string => match (true) {
                        $state === null => 'gray',
                        (float) $state >= 8 => 'success',
                        (float) $state >= 6 => 'warning',
                        default => 'danger',
                    }),
                Tables\Columns\TextColumn::make('questions_completed')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('flow_interruptions')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name'),
                Tables\Filters\SelectFilter::make('language')
                    ->relationship('language', 'name'),
                Tables\Filters\TernaryFilter::make('session_end')
                    ->label('Session Status')
                    ->placeholder('All sessions')
                    ->trueLabel('Completed')
                    ->falseLabel('Active')
                    ->queries(
                        true: fn(Builder $query) => $query->whereNotNull('session_end'),
                        false: fn(Builder $query) => $query->whereNull('session_end'),
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFlowSessions::route('/'),
            'create' => Pages\CreateFlowSession::route('/create'),
            'edit' => Pages\EditFlowSession::route('/{record}/edit'),
        ];
    }
}
