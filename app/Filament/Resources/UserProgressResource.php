<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserProgressResource\Pages;
use App\Filament\Resources\UserProgressResource\RelationManagers;
use App\Models\UserProgress;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserProgressResource extends Resource
{
    protected static ?string $model = UserProgress::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = 'Learning Analytics';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Select::make('language_id')
                    ->relationship('language', 'name')
                    ->required(),
                Forms\Components\Select::make('source_id')
                    ->label('Content Source')
                    ->relationship('contentSource', 'name')
                    ->required()
                    ->searchable(),
                Forms\Components\TextInput::make('current_level')
                    ->required()
                    ->maxLength(100)
                    ->helperText('Current level in the content source'),
                Forms\Components\KeyValue::make('progress_data')
                    ->label('Progress Data')
                    ->required()
                    ->helperText('JSON object with detailed progress information'),
                Forms\Components\KeyValue::make('flow_state')
                    ->label('Flow State')
                    ->required()
                    ->helperText('JSON object with current flow learning state'),
                Forms\Components\KeyValue::make('flow_analytics')
                    ->label('Flow Analytics')
                    ->required()
                    ->helperText('JSON object with flow analytics data'),
                Forms\Components\DateTimePicker::make('last_activity')
                    ->required()
                    ->default(now()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('language.name')
                    ->label('Language')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('contentSource.name')
                    ->label('Content Source')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_level')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_activity')
                    ->dateTime()
                    ->sortable()
                    ->since(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name'),
                Tables\Filters\SelectFilter::make('language')
                    ->relationship('language', 'name'),
                Tables\Filters\SelectFilter::make('contentSource')
                    ->relationship('contentSource', 'name'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserProgress::route('/'),
            'create' => Pages\CreateUserProgress::route('/create'),
            'edit' => Pages\EditUserProgress::route('/{record}/edit'),
        ];
    }
}
