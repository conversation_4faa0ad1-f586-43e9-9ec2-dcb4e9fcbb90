<?php

namespace App\Filament\Resources\ContentSourceResource\Pages;

use App\Filament\Resources\ContentSourceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContentSources extends ListRecords
{
    protected static string $resource = ContentSourceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
