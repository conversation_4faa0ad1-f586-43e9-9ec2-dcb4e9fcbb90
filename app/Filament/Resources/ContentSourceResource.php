<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContentSourceResource\Pages;
use App\Filament\Resources\ContentSourceResource\RelationManagers;
use App\Models\ContentSource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContentSourceResource extends Resource
{
    protected static ?string $model = ContentSource::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static ?string $navigationGroup = 'Content Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('language_id')
                    ->relationship('language', 'name')
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(200),
                Forms\Components\Select::make('type')
                    ->required()
                    ->options([
                        'textbook' => 'Textbook',
                        'course' => 'Course',
                        'custom' => 'Custom',
                        'community' => 'Community',
                    ]),
                Forms\Components\KeyValue::make('configuration')
                    ->label('Configuration')
                    ->required()
                    ->helperText('JSON object with level system, content types, assessment types'),
                Forms\Components\KeyValue::make('flow_patterns')
                    ->label('Flow Patterns')
                    ->required()
                    ->helperText('JSON object with session structure, transition types'),
                Forms\Components\Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID'),
                Tables\Columns\TextColumn::make('language.name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'textbook' => 'Textbook',
                        'course' => 'Course',
                        'custom' => 'Custom',
                        'community' => 'Community',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\QuestionsRelationManager::class,
            RelationManagers\UserProgressRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContentSources::route('/'),
            'create' => Pages\CreateContentSource::route('/create'),
            'edit' => Pages\EditContentSource::route('/{record}/edit'),
        ];
    }
}
