<?php

namespace App\Filament\Resources;

use App\Filament\Resources\QuestionResource\Pages;
use App\Filament\Resources\QuestionResource\RelationManagers;
use App\Models\Question;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QuestionResource extends Resource
{
    protected static ?string $model = Question::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationGroup = 'Content Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('source_id')
                    ->label('Content Source')
                    ->relationship('contentSource', 'name')
                    ->required()
                    ->searchable(),
                Forms\Components\TextInput::make('question_type')
                    ->required()
                    ->maxLength(50)
                    ->helperText('e.g., vocabulary, grammar, reading, listening'),
                Forms\Components\TextInput::make('difficulty_level')
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->maxValue(10)
                    ->helperText('Difficulty level from 1 to 10'),
                Forms\Components\KeyValue::make('content')
                    ->label('Question Content')
                    ->required()
                    ->helperText('JSON object with question data'),
                Forms\Components\KeyValue::make('metadata')
                    ->label('Metadata')
                    ->required()
                    ->helperText('JSON object with additional question metadata'),
                Forms\Components\KeyValue::make('flow_sequence')
                    ->label('Flow Sequence')
                    ->required()
                    ->helperText('JSON object with flow learning sequence data'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('contentSource.name')
                    ->label('Content Source')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('question_type')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('difficulty_level')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ((int) $state) {
                        1, 2, 3 => 'success',
                        4, 5, 6, 7 => 'warning',
                        8, 9, 10 => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('contentSource')
                    ->relationship('contentSource', 'name'),
                Tables\Filters\SelectFilter::make('difficulty_level')
                    ->options([
                        1 => 'Level 1',
                        2 => 'Level 2',
                        3 => 'Level 3',
                        4 => 'Level 4',
                        5 => 'Level 5',
                        6 => 'Level 6',
                        7 => 'Level 7',
                        8 => 'Level 8',
                        9 => 'Level 9',
                        10 => 'Level 10',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestions::route('/'),
            'create' => Pages\CreateQuestion::route('/create'),
            'edit' => Pages\EditQuestion::route('/{record}/edit'),
        ];
    }
}
